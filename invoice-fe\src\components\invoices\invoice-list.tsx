import { useEffect, useState } from "react";
import {
  Search,
  RefreshCw,
  FileText,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { format } from "date-fns";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Badge } from "../ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { useInvoiceStore } from "../../stores/invoice.store";
import { Pagination } from "../ui/pagination";

export function InvoiceList() {
  const {
    invoices,
    isLoading,
    error,
    filters,
    pagination,
    fetchInvoices,
    setFilters,
    setPage,
    setLimit,
    clearError,
  } = useInvoiceStore();

  const [apiToken, setApiToken] = useState(
    localStorage.getItem("apiToken") || ""
  );

  useEffect(() => {
    if (apiToken) {
      localStorage.setItem("apiToken", apiToken);
      fetchInvoices();
    }
  }, [apiToken, fetchInvoices]);

  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  const handleSearch = () => {
    fetchInvoices();
  };

  const handleRefresh = () => {
    fetchInvoices();
  };

  const formatCurrency = (amount: number) => {
    // Handle undefined, null, or NaN values
    if (amount == null || isNaN(amount)) {
      return new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(0);
    }

    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const formatDate = (timestamp: number | string) => {
    if (!timestamp) return "-";
    try {
      // Handle both timestamp (number) and date string
      const date =
        typeof timestamp === "number"
          ? new Date(timestamp)
          : new Date(timestamp);
      return date.toLocaleDateString("vi-VN");
    } catch {
      return typeof timestamp === "string" ? timestamp : "-";
    }
  };

  const formatInvoiceDateTime = (timestamp: number | string) => {
    if (!timestamp) return "-";
    try {
      // Handle both timestamp (number) and date string
      const date =
        typeof timestamp === "number"
          ? new Date(timestamp)
          : new Date(timestamp);
      // Format as MM/DD/YYYY HH:MM AM/PM
      return format(date, "MM/dd/yyyy hh:mm a");
    } catch {
      return "-";
    }
  };

  const getStatusBadge = (paymentStatusName: string, isPaid: boolean) => {
    if (isPaid) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 hover:bg-green-200"
        >
          <CheckCircle2 className="w-3 h-3 mr-1" />
          Paid
        </Badge>
      );
    }

    switch (paymentStatusName?.toLowerCase()) {
      case "paid":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 hover:bg-green-200"
          >
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Paid
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
          >
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case "unpaid":
        return (
          <Badge
            variant="destructive"
            className="bg-red-100 text-red-800 hover:bg-red-200"
          >
            <AlertCircle className="w-3 h-3 mr-1" />
            Unpaid
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">{paymentStatusName || "Unknown"}</Badge>
        );
    }
  };

  return (
    <div className="space-y-4">
      {/* API Token Input */}
      <Card>
        <CardHeader>
          <CardTitle>API Token</CardTitle>
          <CardDescription>
            Enter your API token to access invoice data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            value={apiToken}
            onChange={(e) => setApiToken(e.target.value)}
            type="password"
            placeholder="Enter your API token"
          />
        </CardContent>
      </Card>

      {/* Search Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Filters</CardTitle>
          <CardDescription>
            Filter invoices by date range and other criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <Input
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) => setFilters({ startDate: e.target.value })}
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">End Date</label>
                <Input
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) => setFilters({ endDate: e.target.value })}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Other Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Invoice Number</label>
                <Input
                  value={filters.invoiceNo || ""}
                  onChange={(e) => setFilters({ invoiceNo: e.target.value })}
                  placeholder="Enter invoice number"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Template Code</label>
                <Input
                  value={filters.templateCode || ""}
                  onChange={(e) => setFilters({ templateCode: e.target.value })}
                  placeholder="Enter template code"
                  className="mt-1"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoice Table */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold">
                Invoice List
              </CardTitle>
              <CardDescription className="mt-1">
                Manage and view all invoices matching your search criteria
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="h-8"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
                />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center space-y-4">
                <div className="h-8 w-8 animate-spin rounded-full border-3 border-primary border-t-transparent" />
                <div className="text-center">
                  <p className="text-sm font-medium text-foreground">
                    Loading invoices...
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Please wait while we fetch your data
                  </p>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center space-y-4 text-center">
                <AlertCircle className="h-12 w-12 text-destructive" />
                <div>
                  <p className="text-sm font-medium text-foreground">
                    Error loading invoices
                  </p>
                  <p className="text-xs text-muted-foreground">{error}</p>
                </div>
                <Button variant="outline" onClick={handleRefresh} size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          ) : invoices.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center space-y-4 text-center">
                <FileText className="h-12 w-12 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium text-foreground">
                    No invoices found
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Try adjusting your search criteria
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Summary Info */}
              <div className="flex items-center justify-between mb-4 p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-sm">
                    <span className="font-medium text-foreground">
                      {pagination.total} total invoices
                    </span>
                    <span className="text-muted-foreground ml-2">
                      • Page {pagination.page} of {pagination.totalPages}
                    </span>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}{" "}
                  of {pagination.total} results
                </div>
              </div>

              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50 hover:bg-muted/50">
                      <TableHead className="font-semibold text-foreground w-16">
                        #
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Template
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Series
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Invoice No.
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Buyer Name
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Creator
                      </TableHead>
                      <TableHead className="font-semibold text-foreground text-right">
                        Amount Before Tax
                      </TableHead>
                      <TableHead className="font-semibold text-foreground text-right">
                        Tax Amount
                      </TableHead>
                      <TableHead className="font-semibold text-foreground text-right">
                        Total Amount
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Secret Code
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Status
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Tax Code
                      </TableHead>
                      <TableHead className="font-semibold text-foreground">
                        Created
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoices.map((invoice, index) => (
                      <TableRow
                        key={invoice.invoiceId || `invoice-${index}`}
                        className="hover:bg-muted/30 transition-colors"
                      >
                        <TableCell className="font-medium text-muted-foreground">
                          {((pagination.page || 1) - 1) *
                            (pagination.limit || 10) +
                            index +
                            1}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {invoice.templateCode || "-"}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {invoice.invoiceSeri || "-"}
                        </TableCell>
                        <TableCell className="font-semibold">
                          <div className="flex flex-col">
                            <span className="font-semibold">
                              {invoice.invoiceNo || "-"}
                            </span>
                            <span className="text-xs text-muted-foreground italic mt-0.5">
                              {formatInvoiceDateTime(
                                invoice.issueDate || invoice.createTime
                              )}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell
                          className="max-w-48 truncate"
                          title={invoice.buyerName}
                        >
                          {invoice.buyerName || "-"}
                        </TableCell>
                        <TableCell>{invoice.supplierTaxCode || "-"}</TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(invoice.totalBeforeTax)}
                        </TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(invoice.taxAmount)}
                        </TableCell>
                        <TableCell className="text-right font-mono font-semibold">
                          {formatCurrency(invoice.total)}
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-2 py-1 rounded font-mono">
                            {invoice.originalInvoiceId || "-"}
                          </code>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(
                            invoice.paymentStatusName || "Unknown",
                            invoice.paymentStatus === 1
                          )}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {invoice.buyerTaxCode || "-"}
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(invoice.createTime)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <Pagination
                page={pagination.page}
                pageSize={pagination.limit}
                totalItems={pagination.total}
                totalPages={pagination.totalPages}
                onPageChange={setPage}
                onPageSizeChange={setLimit}
                pageSizeOptions={[10, 25, 50, 100]}
                className="mt-6"
                aria-label="Invoice pagination controls"
              />

              {/* Debug Info (Development Only) */}
              {process.env.NODE_ENV === "development" && (
                <details className="mt-4">
                  <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                    Debug Information
                  </summary>
                  <div className="mt-2 p-3 bg-muted/30 rounded text-xs font-mono">
                    <div>Total Records: {pagination.total}</div>
                    <div>Current Page: {pagination.page}</div>
                    <div>Page Size: {pagination.limit}</div>
                    <div>Total Pages: {pagination.totalPages}</div>
                    <div>Invoices Loaded: {invoices.length}</div>
                    <div>
                      API Token:{" "}
                      {apiToken ? "***" + apiToken.slice(-4) : "Not set"}
                    </div>
                  </div>
                </details>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
